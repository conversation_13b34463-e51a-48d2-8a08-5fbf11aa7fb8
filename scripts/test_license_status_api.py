#!/usr/bin/env python3
"""
Test script for the license status API endpoint.

This script tests the /api/license_status endpoint to ensure it returns
proper license information for the web UI.
"""
import os
import sys
import json
import requests
import time

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def test_license_status_api(base_url="http://localhost:5000"):
    """Test the license status API endpoint."""
    print(f"Testing license status API at {base_url}")
    
    try:
        response = requests.get(f"{base_url}/api/license_status", timeout=10)
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.ok:
            data = response.json()
            print("✅ License status API successful")
            print(f"Response Data: {json.dumps(data, indent=2)}")
            
            # Validate response structure
            required_fields = ['status', 'message', 'available', 'balance', 'server_license', 'dev_bypass']
            missing_fields = [field for field in required_fields if field not in data]
            
            if missing_fields:
                print(f"⚠️  Missing fields in response: {missing_fields}")
            else:
                print("✅ All required fields present in response")
                
            # Check status values
            valid_statuses = ['valid', 'invalid', 'expired', 'error']
            if data.get('status') in valid_statuses:
                print(f"✅ Valid status: {data['status']}")
            else:
                print(f"⚠️  Invalid status: {data.get('status')}")
                
        else:
            print(f"❌ License status API failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error Data: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error Text: {response.text}")
                
    except requests.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return response.ok


def test_with_dev_bypass():
    """Test with development bypass enabled."""
    print("\n" + "="*50)
    print("Testing with development bypass")
    print("="*50)
    
    # Save original value
    original_bypass = os.environ.get("NEXUS_DEV_BYPASS_CREDITS", "")
    
    try:
        # Enable dev bypass
        os.environ["NEXUS_DEV_BYPASS_CREDITS"] = "true"
        
        success = test_license_status_api()
        
        if success:
            print("✅ Dev bypass test completed successfully")
        else:
            print("❌ Dev bypass test failed")
            
    finally:
        # Restore original value
        if original_bypass:
            os.environ["NEXUS_DEV_BYPASS_CREDITS"] = original_bypass
        else:
            os.environ.pop("NEXUS_DEV_BYPASS_CREDITS", None)


def test_without_dev_bypass():
    """Test without development bypass."""
    print("\n" + "="*50)
    print("Testing without development bypass")
    print("="*50)
    
    # Ensure dev bypass is disabled
    os.environ.pop("NEXUS_DEV_BYPASS_CREDITS", None)
    
    success = test_license_status_api()
    
    if success:
        print("✅ Normal mode test completed successfully")
    else:
        print("❌ Normal mode test failed")


def check_web_server_running(base_url="http://localhost:5000"):
    """Check if the web server is running."""
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        return response.ok
    except:
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("License Status API Test Suite")
    print("=" * 60)
    
    # Check if web server is running
    if not check_web_server_running():
        print("❌ Web server is not running at http://localhost:5000")
        print("Please start the web server with: python run_web_ui.py")
        return 1
    
    print("✅ Web server is running")
    
    # Run tests
    test_without_dev_bypass()
    test_with_dev_bypass()
    
    print("\n" + "=" * 60)
    print("Test suite completed")
    print("=" * 60)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
