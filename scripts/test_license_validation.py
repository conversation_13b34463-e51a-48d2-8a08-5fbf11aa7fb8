#!/usr/bin/env python3
"""
Test script for license validation functionality.

This script tests the license validation system to ensure it properly
checks for available licenses before allowing operations to proceed.
"""
import os
import sys
import json

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.core.license_validator import validate_license_for_operations, should_block_operations, get_license_status_message
from agent.core import nexus_credits


def test_license_availability_check():
    """Test the license availability check function."""
    print("Testing license availability check...")
    
    try:
        result = nexus_credits.check_license_availability()
        print(f"License check result: {json.dumps(result, indent=2)}")
        
        if result.get("error"):
            print(f"❌ License check failed: {result['error']}")
        else:
            available = result.get("available", False)
            balance = result.get("balance", 0)
            license_summary = result.get("license_summary", {})
            
            print(f"✅ License check successful:")
            print(f"   Available: {available}")
            print(f"   Balance: {balance}")
            print(f"   License Summary: {json.dumps(license_summary, indent=4)}")
            
    except Exception as e:
        print(f"❌ Exception during license check: {e}")


def test_license_validation():
    """Test the full license validation workflow."""
    print("\nTesting license validation workflow...")
    
    try:
        validation_result = validate_license_for_operations()
        
        print(f"✅ License validation completed:")
        print(f"   Available: {validation_result.available}")
        print(f"   Message: {validation_result.message}")
        print(f"   Details: {json.dumps(validation_result.details, indent=4)}")
        
        status_message = get_license_status_message(validation_result)
        print(f"   Status Message: {status_message}")
        
        should_block = should_block_operations(validation_result)
        print(f"   Should Block Operations: {should_block}")
        
        if should_block:
            print("⚠️  Operations would be blocked due to license issues")
        else:
            print("✅ Operations would be allowed to proceed")
            
    except Exception as e:
        print(f"❌ Exception during license validation: {e}")


def test_dev_bypass():
    """Test the development bypass functionality."""
    print("\nTesting development bypass...")
    
    # Save original value
    original_bypass = os.environ.get("NEXUS_DEV_BYPASS_CREDITS", "")
    
    try:
        # Test with bypass enabled
        os.environ["NEXUS_DEV_BYPASS_CREDITS"] = "true"
        validation_result = validate_license_for_operations()
        
        print(f"✅ Dev bypass test:")
        print(f"   Available: {validation_result.available}")
        print(f"   Message: {validation_result.message}")
        print(f"   Dev Bypass: {validation_result.details.get('dev_bypass', False)}")
        
        if validation_result.available and validation_result.details.get('dev_bypass'):
            print("✅ Development bypass working correctly")
        else:
            print("❌ Development bypass not working as expected")
            
    finally:
        # Restore original value
        if original_bypass:
            os.environ["NEXUS_DEV_BYPASS_CREDITS"] = original_bypass
        else:
            os.environ.pop("NEXUS_DEV_BYPASS_CREDITS", None)


def test_configuration_detection():
    """Test configuration detection."""
    print("\nTesting configuration detection...")
    
    try:
        from agent.core.nexus_credits import _config
        config = _config()
        
        print(f"✅ Configuration detected:")
        print(f"   API Base: {config.get('api_base', 'Not configured')}")
        print(f"   Base URL: {config.get('base_url', 'Not configured')}")
        print(f"   API Key: {'Configured' if config.get('api_key') else 'Not configured'}")
        
        if config.get('api_base'):
            print("✅ Using Nexus API relay mode")
        elif config.get('base_url') and config.get('api_key'):
            print("✅ Using direct website API mode")
        else:
            print("⚠️  No API configuration found")
            
    except Exception as e:
        print(f"❌ Exception during configuration detection: {e}")


def main():
    """Main test function."""
    print("=" * 60)
    print("License Validation Test Suite")
    print("=" * 60)
    
    test_configuration_detection()
    test_license_availability_check()
    test_license_validation()
    test_dev_bypass()
    
    print("\n" + "=" * 60)
    print("Test suite completed")
    print("=" * 60)


if __name__ == "__main__":
    main()
