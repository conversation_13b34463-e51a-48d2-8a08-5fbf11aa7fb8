# License Validation Implementation

This document describes the implementation of license validation in Crucible to ensure operations only run when valid licenses are available.

## Overview

The license validation system checks with the Nexus server before allowing any operations (diagnostics or drive wipes) to proceed. If no valid licenses are available, users are shown an appropriate error message with guidance on how to purchase licenses.

## Architecture

### Core Components

1. **`agent/core/nexus_credits.py`**
   - Added `check_license_availability()` function
   - Checks both credit balance and server license status
   - Returns detailed license information without consuming credits

2. **`agent/core/license_validator.py`** (New)
   - Main validation logic and user-friendly error handling
   - `validate_license_for_operations()` - Main validation function
   - `should_block_operations()` - Determines if operations should be blocked
   - `get_license_status_message()` - Formats status messages for UI

### Integration Points

#### GUI Application (`agent/gui/main_window.py`)
- License validation during startup
- License validation before test execution
- User-friendly error dialogs with purchase guidance

#### Web UI (`web_server/app.py`)
- License validation in `/api/run_tests` endpoint
- License validation in `/api/wipe_drives` endpoint
- Returns HTTP 402 (Payment Required) for license issues

#### JavaScript Frontend
- Enhanced error handling for license-related HTTP responses
- User-friendly alerts with purchase guidance
- Proper error display in both test execution and drive wipe modules

## License Validation Logic

### Availability Criteria
Operations are considered licensed if **any** of the following conditions are met:
1. Credit balance > 0
2. Valid server license with active maintenance

### Validation Flow
1. Check for development bypass (`NEXUS_DEV_BYPASS_CREDITS=true`)
2. Call Nexus API to get balance and license status
3. Evaluate availability criteria
4. Return validation result with appropriate messaging

### Error Handling
- Network errors: Suggest checking server connection
- API key errors: Suggest checking API key configuration
- No configuration: Suggest setting up Nexus API connection
- Insufficient licenses: Provide purchase guidance

## Configuration

### API Configuration
The system supports two configuration modes:

1. **Nexus API Relay Mode** (Recommended)
   - Configure `api_base` in `~/.config/nexus/agent.json`
   - Example: `{"api_base": "http://nexus.lan:8080/api"}`

2. **Direct Website API Mode**
   - Set environment variables:
     - `NEXUS_WEBSITE_BASE_URL`
     - `NEXUS_API_KEY`

### Development Bypass
Set `NEXUS_DEV_BYPASS_CREDITS=true` to bypass license validation during development.

## User Experience

### License Available
- Operations proceed normally
- Status messages show available credits/license info

### License Not Available
- Operations are blocked with clear error messages
- Users receive guidance on purchasing licenses:
  1. Visit the Nexus website
  2. Log in to account
  3. Purchase credits or activate server license
  4. Configure API key in Crucible

### Network/Configuration Issues
- Clear error messages about connection problems
- Guidance on checking server configuration
- Development bypass allows testing without server

## API Responses

### HTTP Status Codes
- `200 OK` - Operation allowed, license valid
- `402 Payment Required` - License required
- `503 Service Unavailable` - License validation failed

### Error Response Format
```json
{
  "error": "License Required",
  "details": "No credits available and no valid server license found.",
  "license_info": {
    "available": false,
    "balance": 0,
    "license_summary": {...}
  }
}
```

## Testing

### Test Script
Run `scripts/test_license_validation.py` to test:
- License availability checking
- Full validation workflow
- Development bypass functionality
- Configuration detection

### Manual Testing
1. **With Valid License**: Operations should proceed normally
2. **Without License**: Operations should be blocked with error messages
3. **Network Issues**: Should show connection error messages
4. **Dev Bypass**: Should allow operations regardless of license status

## Implementation Details

### Startup Validation
- GUI app validates license during startup
- Shows license status in log panel
- Non-blocking - allows app to start even with license issues

### Operation Validation
- Validates license before each operation (tests/wipes)
- Blocks operations if license not available
- Shows user-friendly error dialogs

### Credit Consumption
- Existing credit consumption logic unchanged
- License validation happens **before** credit consumption
- Failed operations still get refunded as before

## Backward Compatibility

- All existing functionality preserved
- Existing credit consumption logic unchanged
- Development bypass ensures testing continues to work
- Graceful degradation when Nexus server unavailable

## Future Enhancements

1. **License Status Display**: Add persistent license status indicator in UI
2. **Automatic Refresh**: Periodically refresh license status
3. **License Expiry Warnings**: Warn users before licenses expire
4. **Offline Mode**: Allow limited operations when server unavailable
