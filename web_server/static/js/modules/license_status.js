// license_status.js
// Module for handling license status display and refresh functionality

let licenseRefreshInterval = null;
const REFRESH_INTERVAL_MS = 60000; // Refresh every minute

/**
 * Initialize the license status module
 */
export function setupLicenseStatus() {
    const refreshBtn = document.getElementById('license-refresh-btn');
    
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshLicenseStatus);
    }
    
    // Load initial license status
    loadLicenseStatus();
    
    // Set up automatic refresh
    startAutoRefresh();
    
    console.log('License status module initialized');
}

/**
 * Load license status from the server
 */
async function loadLicenseStatus() {
    try {
        const response = await fetch('/api/license_status');
        const data = await response.json();
        
        if (response.ok) {
            updateLicenseDisplay(data);
        } else {
            console.error('Failed to load license status:', data);
            updateLicenseDisplay({
                status: 'error',
                message: 'Failed to load license status',
                available: false
            });
        }
    } catch (error) {
        console.error('Error loading license status:', error);
        updateLicenseDisplay({
            status: 'error',
            message: 'Connection error',
            available: false
        });
    }
}

/**
 * Refresh license status (called by refresh button)
 */
async function refreshLicenseStatus() {
    const refreshBtn = document.getElementById('license-refresh-btn');
    const statusText = document.getElementById('license-status-text');
    
    // Disable button and show loading state
    if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.textContent = '⟳';
    }
    
    if (statusText) {
        statusText.textContent = 'Refreshing...';
        statusText.className = 'license-unknown';
    }
    
    try {
        await loadLicenseStatus();
    } finally {
        // Re-enable button
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.textContent = '🔄';
        }
    }
}

/**
 * Update the license status display
 * @param {Object} data - License status data from server
 */
function updateLicenseDisplay(data) {
    const statusText = document.getElementById('license-status-text');
    
    if (!statusText) {
        console.warn('License status text element not found');
        return;
    }
    
    // Clear existing classes
    statusText.className = '';
    
    // Update display based on status
    switch (data.status) {
        case 'valid':
            statusText.className = 'license-valid';
            statusText.textContent = `✓ ${data.message}`;
            break;
            
        case 'expired':
            statusText.className = 'license-expired';
            statusText.textContent = `⚠ ${data.message}`;
            break;
            
        case 'invalid':
            statusText.className = 'license-invalid';
            statusText.textContent = `✗ ${data.message}`;
            break;
            
        case 'error':
            statusText.className = 'license-unknown';
            statusText.textContent = `? ${data.message}`;
            break;
            
        default:
            statusText.className = 'license-unknown';
            statusText.textContent = '? Unknown status';
            break;
    }
    
    // Add dev bypass indicator if applicable
    if (data.dev_bypass) {
        statusText.textContent += ' (DEV)';
    }
    
    // Update tooltip with detailed information
    let tooltip = `License Status: ${data.status}\n`;
    tooltip += `Available: ${data.available ? 'Yes' : 'No'}\n`;
    tooltip += `Credits: ${data.balance || 0}\n`;
    
    if (data.server_license && data.server_license.has_license) {
        tooltip += `Server License: ${data.server_license.server_count} servers\n`;
        tooltip += `Maintenance: ${data.server_license.maintenance_active ? 'Active' : 'Expired'}\n`;
        if (data.server_license.maintenance_expiry) {
            tooltip += `Expires: ${data.server_license.maintenance_expiry.substring(0, 10)}\n`;
        }
    }
    
    statusText.title = tooltip;
    
    console.log('License status updated:', data);
}

/**
 * Start automatic license status refresh
 */
function startAutoRefresh() {
    // Clear any existing interval
    if (licenseRefreshInterval) {
        clearInterval(licenseRefreshInterval);
    }
    
    // Set up new interval
    licenseRefreshInterval = setInterval(() => {
        loadLicenseStatus();
    }, REFRESH_INTERVAL_MS);
    
    console.log(`License auto-refresh started (${REFRESH_INTERVAL_MS / 1000}s interval)`);
}

/**
 * Stop automatic license status refresh
 */
export function stopAutoRefresh() {
    if (licenseRefreshInterval) {
        clearInterval(licenseRefreshInterval);
        licenseRefreshInterval = null;
        console.log('License auto-refresh stopped');
    }
}

/**
 * Get current license status (for other modules to use)
 * @returns {Promise<Object>} License status data
 */
export async function getLicenseStatus() {
    try {
        const response = await fetch('/api/license_status');
        if (response.ok) {
            return await response.json();
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        console.error('Error getting license status:', error);
        return {
            status: 'error',
            message: 'Failed to get license status',
            available: false
        };
    }
}

/**
 * Check if operations should be allowed based on license status
 * @returns {Promise<boolean>} True if operations are allowed
 */
export async function checkLicenseForOperations() {
    const status = await getLicenseStatus();
    return status.available || status.dev_bypass;
}
