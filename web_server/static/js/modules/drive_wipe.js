import { fetchData } from './utils.js';

let wipeProgressInterval = null; // internal module state

// --- Drive Wipe Functionality (extracted from main.js) ---
export function setupDriveWipe() {
    const loadDrivesBtn = document.getElementById('load-drives-btn');
    const wipeMethodSelect = document.getElementById('wipe-method-select');
    const startWipeBtn = document.getElementById('start-wipe-btn');
    const cancelWipeBtn = document.getElementById('cancel-wipe-btn');

    if (loadDrivesBtn) loadDrivesBtn.addEventListener('click', loadDrivesForWipe);
    if (startWipeBtn) startWipeBtn.addEventListener('click', startWipe);
    if (cancelWipeBtn) cancelWipeBtn.addEventListener('click', cancelWipe);

    if (wipeMethodSelect) {
        wipeMethodSelect.addEventListener('change', () => {
            const selectedDrives = document.querySelectorAll('input[name="selected_drive_wipe"]:checked');
            if (startWipeBtn) {
                startWipeBtn.disabled = selectedDrives.length === 0 || wipeMethodSelect.value === '' || wipeProgressInterval !== null;
            }
        });
    }

    // Automatically load/refresh drives each time the Secure Drive Wipe section is expanded
    const wipeSection = document.getElementById('drive-wipe-section');
    if (wipeSection) {
        const observer = new MutationObserver(() => {
            if (wipeSection.classList.contains('expanded')) {
                loadDrivesForWipe();
            }
        });
        observer.observe(wipeSection, { attributes: true, attributeFilter: ['class'] });
    }
}

async function loadWipeMethods() {
    const wipeMethodSelect = document.getElementById('wipe-method-select');
    const wipeStatusMessage = document.getElementById('wipe-status-message');
    if (!wipeMethodSelect || !wipeStatusMessage) return;

    try {
        const methods = await fetchData('/api/wipe_methods') || [];
        wipeMethodSelect.innerHTML = '<option value="">-- Select Wipe Method --</option>';
        methods.forEach(method => {
            const option = document.createElement('option');
            option.value = method.key;
            option.textContent = method.name;
            option.title = method.description;
            wipeMethodSelect.appendChild(option);
        });
        wipeMethodSelect.disabled = false;
    } catch (error) {
        console.error('Error loading wipe methods:', error);
        wipeStatusMessage.textContent = `Error loading wipe methods: ${error.message}`;
        wipeMethodSelect.disabled = true;
    }
}

async function loadDrivesForWipe() {
    const driveListContainer = document.getElementById('drive-list-container');
    const startWipeBtn = document.getElementById('start-wipe-btn');
    const wipeMethodSelect = document.getElementById('wipe-method-select');
    if (!driveListContainer || !startWipeBtn) return;

    driveListContainer.innerHTML = '<p>Loading drives...</p>';
    startWipeBtn.disabled = true;

    try {
        const drives = await fetchData('/api/drives') || [];
        driveListContainer.innerHTML = '';

        if (drives.length === 0) {
            driveListContainer.innerHTML = '<p>No drives found or accessible.</p>';
            return;
        }

        const table = document.createElement('table');
        table.className = 'info-table drive-wipe-table';
        table.innerHTML = `
            <thead><tr>
                <th>Select</th><th>Path</th><th>Model</th><th>Size (GB)</th><th>Type</th><th>Serial</th><th>Mountpoints</th>
            </tr></thead>`;
        const tbody = document.createElement('tbody');
        drives.forEach(drive => {
            const row = tbody.insertRow();
            const cbCell = row.insertCell();
            const cb = document.createElement('input');
            cb.type = 'checkbox';
            cb.name = 'selected_drive_wipe';
            cb.value = drive.path;
            cb.id = `drive_wipe_cb_${drive.path.replace(/[^a-zA-Z0-9]/g, '_')}`;
            // Warn if drive has mounted partitions but still allow selection
            if (drive.mountpoints && drive.mountpoints.length) {
                cb.title = `Mounted at: ${drive.mountpoints.join(', ')}`;
                row.classList.add('has-mountpoints');
            }
            cbCell.appendChild(cb);
            // Update recommended method and button state when selection changes
            cb.addEventListener('change', () => {
                updateRecommendedMethod();
                const selected = document.querySelectorAll('input[name="selected_drive_wipe"]:checked');
                startWipeBtn.disabled = selected.length === 0 || wipeMethodSelect.value === '' || wipeProgressInterval !== null;
            });
            row.insertCell().textContent = drive.path || 'N/A';
            row.insertCell().textContent = drive.model || 'N/A';
            row.insertCell().textContent = drive.size_gb || 'N/A';
            row.insertCell().textContent = drive.type || 'N/A';
            row.insertCell().textContent = drive.serial || 'N/A';
            row.insertCell().textContent = (drive.mountpoints && drive.mountpoints.length) ? drive.mountpoints.join(', ') : 'None';
        });
        table.appendChild(tbody);
        driveListContainer.appendChild(table);
        loadWipeMethods();
    } catch (error) {
        console.error('Error loading drives:', error);
        driveListContainer.innerHTML = `<p style="color:red;">${error.message}</p>`;
    }
}

async function startWipe() {
    const selected = Array.from(document.querySelectorAll('input[name="selected_drive_wipe"]:checked')).map(cb => cb.value);
    const methodKey = document.getElementById('wipe-method-select').value;
    if (!selected.length) { alert('Select at least one drive.'); return; }
    if (!methodKey) { alert('Select a wipe method.'); return; }

    // Check license status before proceeding
    try {
        const { checkLicenseForOperations } = await import('./license_status.js');
        const licenseAllowed = await checkLicenseForOperations();

        if (!licenseAllowed) {
            alert('License Required\n\nNo valid licenses available for drive wipe operations.\n\nTo purchase licenses:\n1. Visit the Nexus website\n2. Log in to your account\n3. Purchase credits or activate a server license\n4. Configure your API key in Crucible');
            return;
        }
    } catch (error) {
        console.warn('License check failed, proceeding anyway:', error);
    }

    if (!confirm(`This will irreversibly destroy data on: ${selected.join(', ')}. Continue?`)) return;

    disableWipeControls(true);
    addWipeLogMessage('Initiating wipe...', 'info');

    try {
        const response = await fetch('/api/wipe_drives', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ drives: selected, method_key: methodKey })
        });
        const result = await response.json();

        if (!response.ok) {
            // Handle license-specific errors
            if (response.status === 402) {
                // License required error
                const licenseMessage = result.details || 'No valid licenses available for drive wipe operations.';
                const guidance = 'To purchase licenses:\n1. Visit the Nexus website\n2. Log in to your account\n3. Purchase credits or activate a server license\n4. Configure your API key in Crucible';
                alert(`License Required\n\n${licenseMessage}\n\n${guidance}`);
                addWipeLogMessage(`License Required: ${licenseMessage}`, 'error');
            } else if (response.status === 503 && result.error === 'License Validation Error') {
                // License validation error
                alert(`License Validation Error\n\n${result.details}\n\nPlease check your Nexus server connection.`);
                addWipeLogMessage(`License Validation Error: ${result.details}`, 'error');
            } else {
                // Other errors
                throw new Error(result.error || 'Failed to start wipe');
            }
            disableWipeControls(false, true);
            return;
        }

        addWipeLogMessage(result.message || 'Wipe process started.', 'info');
        if (wipeProgressInterval) clearInterval(wipeProgressInterval);
        wipeProgressInterval = setInterval(updateWipeProgress, 1500);
    } catch (e) {
        console.error(e);
        addWipeLogMessage(e.message, 'error');
        disableWipeControls(false, true);
    }
}

async function updateWipeProgress() {
    const bar = document.getElementById('wipe-progress-bar');
    const statusMsg = document.getElementById('wipe-status-message');
    const logOutput = document.getElementById('wipe-log-output');
    if (!bar || !statusMsg || !logOutput) { stopPolling(true); return; }

    try {
        const data = await fetchData('/api/wipe_progress');
        bar.style.width = `${data.progress || 0}%`;
        bar.textContent = `${Math.round(data.progress || 0)}%`;
        statusMsg.textContent = `Status: ${data.message || data.status}`;
        (data.logs || []).slice(logOutput.children.length).forEach(m => addWipeLogMessage(m));
        if (['complete', 'cancelled', 'error'].includes(data.status)) {
            stopPolling(data.status === 'error');
        }
    } catch (e) {
        console.error(e);
        addWipeLogMessage(`Error: ${e.message}`, 'error');
    }
}

function addWipeLogMessage(msg, type = 'log') {
    const out = document.getElementById('wipe-log-output');
    if (!out) return;
    const div = document.createElement('div');
    div.textContent = msg;
    div.className = `log-entry ${type}`;
    out.appendChild(div);
    out.scrollTop = out.scrollHeight;
}

function stopPolling(isError = false) {
    if (wipeProgressInterval) { clearInterval(wipeProgressInterval); wipeProgressInterval = null; }
    disableWipeControls(false, isError);
}

function disableWipeControls(disable, isError = false) {
    const start = document.getElementById('start-wipe-btn');
    const cancel = document.getElementById('cancel-wipe-btn');
    const load = document.getElementById('load-drives-btn');
    const methodSel = document.getElementById('wipe-method-select');

    if (start) start.disabled = disable;
    if (cancel) cancel.disabled = !disable;
    if (load) load.disabled = disable;
    if (methodSel) methodSel.disabled = disable;

    const bar = document.getElementById('wipe-progress-bar');
    if (bar && isError) bar.classList.add('error');
}

// Automatically recommend a wipe method based on selected drive types
function updateRecommendedMethod() {
    const wipeMethodSelect = document.getElementById('wipe-method-select');
    if (!wipeMethodSelect) return;
    const selectedCbs = Array.from(document.querySelectorAll('input[name="selected_drive_wipe"]:checked'));
    if (selectedCbs.length === 0) return;
    const types = selectedCbs.map(cb => {
        const row = cb.closest('tr');
        return row ? row.cells[4].textContent.trim() : '';
    });
    const uniq = [...new Set(types)];
    let recommended = '';
    if (uniq.length === 1) {
        const t = uniq[0];
        if (t === 'NVMe') recommended = 'nvme_sanitize';
        else if (t === 'SSD') recommended = 'ata_secure_erase';
        else recommended = 'zero_fill';
    } else {
        recommended = 'zero_fill';
    }
    if (wipeMethodSelect.value !== recommended) {
        wipeMethodSelect.value = recommended;
        wipeMethodSelect.dispatchEvent(new Event('change'));
    }
}


async function cancelWipe() {
    if (!confirm('Attempt to cancel the wipe?')) return;
    addWipeLogMessage('Attempting to cancel wipe...', 'warn');
    try {
        const response = await fetch('/api/wipe_drives/cancel', { method: 'POST' });
        const result = await response.json();
        if (!response.ok) throw new Error(result.error || 'Failed to cancel');
        addWipeLogMessage(result.message || 'Cancel request sent.', 'info');
        stopPolling();
    } catch (e) {
        console.error(e);
        addWipeLogMessage(e.message, 'error');
    }
}
