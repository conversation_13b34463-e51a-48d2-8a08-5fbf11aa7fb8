import { fetchData } from './utils.js';

// Ensure a Nexus profile is saved to the local Crucible backend
async function ensureLocalProfile(profile) {
    try {
        if (!profile || !profile.name) return false;

        // Prefer idempotent update first to avoid 409 noise; if not found, create
        const putRes = await fetch(`/api/profiles/${encodeURIComponent(profile.name)}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(profile)
        });
        if (putRes.ok) return true; // Updated existing

        if (putRes.status === 404) {
            const postRes = await fetch('/api/profiles', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(profile)
            });
            return postRes.ok; // Created new
        }

        return false;
    } catch (_) {
        return false;
    }
}

// --- Nexus Integration (config + cache) ---
// Prefer nexus.lan only when actually using that hostname; otherwise, default to localhost:8080
const DEFAULT_NEXUS_BASE = (window.location && window.location.hostname === 'nexus.lan')
    ? 'http://nexus.lan'
    : 'http://localhost:8080';
// override via localStorage.nexus_base_url or window.NEXUS_BASE_URL
function getNexusBase() {
    const base = (window.NEXUS_BASE_URL || localStorage.getItem('nexus_base_url') || DEFAULT_NEXUS_BASE);
    return (typeof base === 'string' ? base : String(base || '')).replace(/\/$/, '');
}

let nexusProfilesByName = new Map(); // name -> payload object
let nexusIdsByName = new Map();      // name -> numeric id (for DELETE/PATCH)
let nexusProfilesLoaded = false;
let nexusDefaultProfileName = null;

async function tryLoadProfilesFromNexus() {
    const base = getNexusBase();
    try {
        // Get full rows to map default_id -> payload.name
        const rows = await (await fetch(`${base}/api/profiles`)).json();
        const payloads = await (await fetch(`${base}/api/profiles/crucible`)).json();
        const defRes = await (await fetch(`${base}/api/profiles/default`)).json();

        nexusProfilesByName = new Map();
        nexusIdsByName = new Map();
        // rows and payloads are parallel order; build name map, keep defensive checks
        payloads.forEach((p, idx) => {
            const row = rows[idx];
            const name = p?.name || row?.name;
            if (name) {
                nexusProfilesByName.set(name, p || {});
                if (row && typeof row.id === 'number') {
                    nexusIdsByName.set(name, row.id);
                }
            }
        });

        // Resolve default name if possible
        if (defRes && typeof defRes.default_id === 'number') {
            const match = rows.find(r => r.id === defRes.default_id);
            if (match) {
                try { nexusDefaultProfileName = JSON.parse(match.payload || '{}').name || match.name; }
                catch (_) { nexusDefaultProfileName = match.name; }
            }
        }

        nexusProfilesLoaded = nexusProfilesByName.size > 0;
    } catch (e) {
        console.warn('Nexus profiles fetch failed, will fall back to local API.', e);
        nexusProfilesByName.clear();
        nexusProfilesLoaded = false;
        nexusDefaultProfileName = null;
    }
}

// To be populated from the backend
let availableTests = [];

// --- Core Profile Loading & Display ---

async function loadProfiles() {
    const profileSelect = document.getElementById('profile-select');
    const profileSelectQuick = document.getElementById('profile-select-quick');

    // Try Nexus first
    if (!nexusProfilesLoaded) {
        await tryLoadProfilesFromNexus();
    }

    let optionHTML = '<option value="">-- Select Profile --</option>';
    if (nexusProfilesLoaded) {
        const names = Array.from(nexusProfilesByName.keys()).sort();
        optionHTML += names.map(n => `<option value="${n}">${n}</option>`).join('');
    } else {
        // Fallback to local Crucible API
        const profiles = await fetchData('/api/profiles');
        if (!profiles) {
            if (profileSelect) profileSelect.innerHTML = '<option value="">Error loading profiles</option>';
            if (profileSelectQuick) profileSelectQuick.innerHTML = '<option value="">Error loading profiles</option>';
            return;
        }
        optionHTML += profiles.map(profile => `<option value="${profile.name}">${profile.name}</option>`).join('');
    }

    if (profileSelect) profileSelect.innerHTML = optionHTML;
    if (profileSelectQuick) profileSelectQuick.innerHTML = optionHTML;

    // Preselect default from Nexus if available
    if (nexusProfilesLoaded && nexusDefaultProfileName) {
        if (profileSelect) profileSelect.value = nexusDefaultProfileName;
        if (profileSelectQuick) profileSelectQuick.value = nexusDefaultProfileName;
    }

    // After loading, ensure buttons are in correct state
    const editBtn = document.getElementById('edit-profile-btn');
    const deleteBtn = document.getElementById('delete-profile-btn');
    if(editBtn) editBtn.disabled = true;
    if(deleteBtn) deleteBtn.disabled = true;

    const profileDetails = document.getElementById('profile-details');
    if (profileDetails) profileDetails.innerHTML = '';
}

async function displaySelectedProfileDetails() {
    const profileSelect = document.getElementById('profile-select');
    const profileDetailsDiv = document.getElementById('profile-details');
    if (!profileSelect || !profileDetailsDiv) return;

    const profileName = profileSelect.value;
    if (!profileName) {
        profileDetailsDiv.innerHTML = '<p>Please select a profile to see details.</p>';
        return;
    }

    profileDetailsDiv.innerHTML = '<p>Loading details...</p>';
    let profile = null;
    let createdLocally = false;
    if (nexusProfilesLoaded) {
        // Use Nexus payload and materialize locally without probing (avoids GET 404)
        profile = nexusProfilesByName.get(profileName) || null;
        if (profile) {
            createdLocally = await ensureLocalProfile(profile);
        }
    } else {
        profile = await fetchData(`/api/profiles/${encodeURIComponent(profileName)}`);
    }
    if (!profile) {
        profileDetailsDiv.innerHTML = `<p>Error loading details for ${profileName}.</p>`;
        return;
    }

    let html = `<h3>${profile.name}</h3>`;
    html += `<p><strong>Description:</strong> ${profile.description || 'N/A'}</p>`;
    html += `<p><strong>Device Type:</strong> ${profile.device_type || 'N/A'}</p>`;
    html += `<p><strong>Tests:</strong></p><ul>`;
    if (profile.tests && profile.tests.length > 0) {
        profile.tests.forEach(test => {
            html += `<li>${test}</li>`;
        });
    } else {
        html += `<li>No tests configured.</li>`;
    }
    html += `</ul>`;
    
    // Display RAM test configuration if present
    const ramTestPaths = [
        'agent.tests.visual_ram_test.run_visual_ram_test',
        'agent.tests.web_visual_ram_test.run_web_visual_ram_test'
    ];
    
    let ramConfig = null;
    if (profile.test_args) {
        for (const path of ramTestPaths) {
            if (profile.test_args[path] && 
                (profile.test_args[path].test_size_mode || profile.test_args[path].test_size_value)) {
                ramConfig = profile.test_args[path];
                break;
            }
        }
    }
    
    if (ramConfig) {
        html += `<p><strong>RAM Test Configuration:</strong></p>`;
        html += `<ul>`;
        html += `<li><strong>Mode:</strong> ${ramConfig.test_size_mode === 'percentage' ? 'Percentage' : 'Absolute'}</li>`;
        html += `<li><strong>Size:</strong> ${ramConfig.test_size_value}${ramConfig.test_size_mode === 'percentage' ? '%' : ' MB'}</li>`;
        html += `<li><strong>Duration:</strong> ${ramConfig.duration_seconds || 30} seconds</li>`;
        html += `</ul>`;
    }
    
    // Display CPU test configuration if present
    const cpuTestPaths = [
        'agent.tests.visual_cpu_test.visual_cpu_test'
    ];
    
    let cpuConfig = null;
    if (profile.test_args) {
        for (const path of cpuTestPaths) {
            if (profile.test_args[path] && 
                (profile.test_args[path].duration_seconds || profile.test_args[path].cpu_load_intensity)) {
                cpuConfig = profile.test_args[path];
                break;
            }
        }
    }
    
    if (cpuConfig) {
        html += `<p><strong>CPU Test Configuration:</strong></p>`;
        html += `<ul>`;
        html += `<li><strong>Duration:</strong> ${cpuConfig.duration_seconds || 20} seconds</li>`;
        html += `<li><strong>CPU Load Intensity:</strong> ${cpuConfig.cpu_load_intensity || 100}%</li>`;
        html += `</ul>`;
    }
    
    if (profile.test_args && Object.keys(profile.test_args).length > 0) {
        html += `<p><strong>Test Arguments:</strong></p><pre>${JSON.stringify(profile.test_args, null, 2)}</pre>`;
    } else {
        html += `<p><strong>Test Arguments:</strong> None</p>`;
    }
    // Append validation details — only if the profile exists locally (after auto-materialize if needed)
    try {
        if (!nexusProfilesLoaded || createdLocally) {
            const validation = await fetchData(`/api/profiles/validate/${encodeURIComponent(profileName)}`);
            if (validation) {
                if (validation.missing_count === 0) {
                    html += `<p style=\"color:green;\">All tests resolved successfully.</p>`;
                } else {
                    html += `<p style=\"color:#f44336;\">${validation.missing_count} test(s) unavailable</p>`;
                }
            }
        }
    } catch (e) {
        console.warn('Validation check skipped or failed:', e);
    }

    profileDetailsDiv.innerHTML = html;
}

// displayProfileDetailsQuick function removed - profile details are now managed in Nexus


// --- Modal & Form Logic ---

function setupProfileModal() {
    const modal = document.getElementById('profile-modal');
    const closeButton = document.querySelector('#profile-modal .close-button');
    const profileForm = document.getElementById('profile-form');

    // Ensure a robust close function is available
    if (!window.closeProfileModal) {
        window.closeProfileModal = function closeProfileModal() {
            const m = document.getElementById('profile-modal');
            if (m) m.style.display = 'none';
        };
    }

    if (closeButton) {
        closeButton.onclick = () => window.closeProfileModal();
    }

    // Only close when clicking on the profile modal backdrop itself
    window.addEventListener('click', (event) => {
        if (event.target === modal) {
            window.closeProfileModal();
        }
    });

    if (profileForm) {
        profileForm.addEventListener('submit', handleProfileFormSubmit);
    }
    // Populate available tests by fetching from the new endpoint
    fetchAndPopulateAvailableTests();
    
    // Setup RAM configuration UI
    setupRamConfigurationUI();
    
    // Setup CPU configuration UI
    setupCpuTestConfiguration();
}

async function fetchAndPopulateAvailableTests() {
    const testsData = await fetchData('/api/available_tests');
    if (testsData && Array.isArray(testsData)) {
        populateTestCheckboxes(testsData);
    } else {
        console.error("Could not fetch or parse available tests. Using fallback or empty list.");
        populateTestCheckboxes([]);
    }
}

function populateTestCheckboxes(testsWithOptions) {
    availableTests = testsWithOptions;
    const checkboxesContainer = document.getElementById('profile-tests-checkboxes');
    if (!checkboxesContainer) return;

    checkboxesContainer.innerHTML = '';
    if (testsWithOptions.length === 0) {
        checkboxesContainer.innerHTML = '<p>No tests available or could not load test list.</p>';
        return;
    }

    testsWithOptions.forEach(testOption => {
        const label = document.createElement('label');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.name = 'profile-test';
        checkbox.value = testOption.path;

        label.appendChild(checkbox);
        label.appendChild(document.createTextNode(` ${testOption.name}`));
        checkboxesContainer.appendChild(label);
    });
    
    // Update RAM config visibility after populating checkboxes
    setTimeout(() => updateRamConfigVisibility(), 100);
}

async function openProfileModalForCreate() {
    const modal = document.getElementById('profile-modal');
    const modalTitle = document.getElementById('profile-modal-title');
    const profileForm = document.getElementById('profile-form');

    if (!modal || !modalTitle || !profileForm) return;

    modalTitle.textContent = 'Create Profile';
    profileForm.reset();
    document.getElementById('profile-id').value = '';
    document.getElementById('profile-name').disabled = false;

    const checkboxes = profileForm.querySelectorAll('input[name="profile-test"]');
    checkboxes.forEach(cb => cb.checked = false);

    // Reset RAM configuration to defaults
    setRamConfigInForm(null);
    
    // Reset CPU configuration to defaults
    setCpuConfigInForm(null);

    modal.style.display = 'block';
}

async function openProfileModalForEdit() {
    const modal = document.getElementById('profile-modal');
    const modalTitle = document.getElementById('profile-modal-title');
    const profileForm = document.getElementById('profile-form');
    const profileSelect = document.getElementById('profile-select') || document.getElementById('profile-select-quick');

    if (!modal || !modalTitle || !profileForm) return;

    // Determine selected profile name from either main select (if present) or quick select
    const profileName = profileSelect ? profileSelect.value : '';
    if (!profileName) {
        alert('Please select a profile to edit.');
        return;
    }

    // Always ensure available tests and checkboxes are populated BEFORE fetching the profile.
    // This prevents a race on first invocation after page load where no checkboxes exist yet.
    if (!availableTests || availableTests.length === 0) {
        await fetchAndPopulateAvailableTests();
    } else {
        populateTestCheckboxes(availableTests);
    }

    // Yield to the browser to render the checkboxes before we set checked states.
    await new Promise(requestAnimationFrame);

    const profile = await fetchData(`/api/profiles/${encodeURIComponent(profileName)}`);
    if (!profile) {
        alert(`Could not load profile: ${profileName}`);
        return;
    }

    modalTitle.textContent = 'Edit Profile';
    profileForm.reset();

    // Re-populate again to be absolutely sure the DOM list exists after form.reset()
    populateTestCheckboxes(availableTests || []);

    // Keep the original name in a hidden field for collision/rename logic,
    // but allow editing the visible name field so users can rename.
    document.getElementById('profile-id').value = profile.name;
    document.getElementById('profile-name').value = profile.name;
    document.getElementById('profile-name').disabled = false;
    document.getElementById('profile-description').value = profile.description || '';
    document.getElementById('profile-device-type').value = profile.device_type || '';
    document.getElementById('profile-test-args').value = profile.test_args ? JSON.stringify(profile.test_args, null, 2) : '{}';

    // Map profile.tests to checkbox values after the checkbox DOM is guaranteed to exist.
    const selectedSet = new Set(profile.tests || []);
    const applyCheckedState = () => {
        const cbs = profileForm.querySelectorAll('input[name="profile-test"]');
        cbs.forEach(cb => { cb.checked = selectedSet.has(cb.value); });
        // Update dependent config sections now that checked states are applied
        updateRamConfigVisibility();
        updateCpuConfigVisibility();
    };

    // If the container is still empty (e.g., extremely slow load), retry on next frame once.
    const container = document.getElementById('profile-tests-checkboxes');
    if (!container || container.children.length === 0) {
        await new Promise(requestAnimationFrame);
    }
    applyCheckedState();

    // Load RAM configuration from profile test_args
    const ramTestPaths = [
        'agent.tests.visual_ram_test.run_visual_ram_test',
        'agent.tests.web_visual_ram_test.run_web_visual_ram_test'
    ];
    let ramConfig = null;
    if (profile.test_args) {
        for (const path of ramTestPaths) {
            if (profile.test_args[path]) {
                ramConfig = profile.test_args[path];
                break;
            }
        }
    }
    setRamConfigInForm(ramConfig);

    // Load CPU configuration from profile test_args
    const cpuTestPaths = [
        'agent.tests.visual_cpu_test.visual_cpu_test'
    ];
    let cpuConfig = null;
    if (profile.test_args) {
        for (const path of cpuTestPaths) {
            if (profile.test_args[path] &&
                (profile.test_args[path].duration_seconds || profile.test_args[path].cpu_load_intensity)) {
                cpuConfig = profile.test_args[path];
                break;
            }
        }
    }
    setCpuConfigInForm(cpuConfig);

    // Ensure modal is visible
    modal.style.display = 'block';
}

async function handleProfileFormSubmit(event) {
    console.log('DEBUG: Profile form submit started');
    event.preventDefault();
    const form = event.target;
    const profileId = document.getElementById('profile-id').value;
    const profileName = document.getElementById('profile-name').value;
    
    console.log('DEBUG: Profile ID:', profileId, 'Profile Name:', profileName);

    const selectedTests = Array.from(form.querySelectorAll('input[name="profile-test"]:checked'))
                              .map(cb => cb.value);
    console.log('DEBUG: Selected tests:', selectedTests);
    
    // Validate RAM configuration if visible
    const ramConfigSection = document.getElementById('ram-config-section');
    console.log('DEBUG: RAM config section display:', ramConfigSection?.style.display);
    if (ramConfigSection && ramConfigSection.style.display !== 'none') {
        console.log('DEBUG: Validating RAM configuration');
        const ramSizeValid = validateRamSizeInput();
        const ramDurationValid = validateRamDurationInput();
        console.log('DEBUG: RAM validation results - size:', ramSizeValid, 'duration:', ramDurationValid);
        
        if (!ramSizeValid || !ramDurationValid) {
            console.log('DEBUG: RAM validation failed, stopping save');
            alert('Please correct the RAM configuration errors before saving.');
            return;
        }
    }
    
    // Validate CPU configuration if visible
    console.log('DEBUG: About to validate CPU configuration');
    const cpuConfigValid = validateCpuTestConfig();
    console.log('DEBUG: CPU validation result:', cpuConfigValid);
    if (!cpuConfigValid) {
        console.log('DEBUG: CPU validation failed, stopping save');
        alert('Please correct the CPU configuration errors before saving.');
        return;
    }
    
    let testArgs = {};
    try {
        const testArgsRaw = document.getElementById('profile-test-args').value.trim();
        if (testArgsRaw) {
            testArgs = JSON.parse(testArgsRaw);
        }
    } catch (e) {
        alert('Test Arguments are not valid JSON. Please correct them.');
        return;
    }

    // Add RAM configuration to test_args if RAM tests are selected
    const ramConfig = getRamConfigFromForm();
    if (ramConfig) {
        const ramTestPaths = [
            'agent.tests.visual_ram_test.run_visual_ram_test',
            'agent.tests.web_visual_ram_test.run_web_visual_ram_test'
        ];
        
        // Add RAM config to each selected RAM test
        ramTestPaths.forEach(testPath => {
            if (selectedTests.includes(testPath)) {
                testArgs[testPath] = { ...testArgs[testPath], ...ramConfig };
            }
        });
    }

    // Add CPU configuration to test_args if CPU tests are selected
    const cpuConfig = getCpuConfigFromForm();
    if (cpuConfig) {
        const cpuTestPaths = [
            'agent.tests.visual_cpu_test.visual_cpu_test'
        ];
        
        // Add CPU config to each selected CPU test
        cpuTestPaths.forEach(testPath => {
            if (selectedTests.includes(testPath)) {
                testArgs[testPath] = { ...testArgs[testPath], ...cpuConfig };
            }
        });
    }

    const profileData = {
        name: profileName,
        description: document.getElementById('profile-description').value,
        device_type: document.getElementById('profile-device-type').value,
        tests: selectedTests,
        test_args: testArgs
    };

    let response;
    // If we have an original id (editing) and the name did not change, do a simple PUT update.
    if (profileId && profileId === profileName) {
        response = await fetch(`/api/profiles/${encodeURIComponent(profileId)}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(profileData)
        });
    } else {
        // Either creating new OR renaming (profileId set but name changed).
        if (!profileName.trim()) {
            alert("Profile name cannot be empty.");
            return;
        }
        response = await fetch('/api/profiles', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(profileData)
        });
        // If this was a rename (old id exists and differs), attempt to delete the old profile to complete the rename.
        if (response.ok && profileId && profileId !== profileName) {
            try {
                await fetch(`/api/profiles/${encodeURIComponent(profileId)}`, { method: 'DELETE' });
            } catch (e) {
                console.warn('Old profile delete after rename failed:', e);
            }
        }
    }

    if (response.ok) {
        alert(`Profile ${profileId && profileId !== profileName ? 'renamed' : (profileId ? 'updated' : 'created')} successfully!`);
        document.getElementById('profile-modal').style.display = 'none';
        await loadProfiles();
        // Sync selects to the new/updated name where applicable
        const profileSelect = document.getElementById('profile-select');
        const profileSelectQuick = document.getElementById('profile-select-quick');
        if (profileSelect) profileSelect.value = profileName;
        if (profileSelectQuick) profileSelectQuick.value = profileName;
        // Refresh quick details if visible
        displayProfileDetailsQuick(profileName);
    } else {
        let errorText = 'Failed to save profile.';
        try {
            const errorData = await response.json();
            if (errorData && (errorData.error || errorData.message)) {
                errorText = errorData.error || errorData.message;
            }
        } catch (_) {}
        alert(`Error: ${errorText}`);
    }
}

async function deleteSelectedProfile() {
    const profileSelect = document.getElementById('profile-select');
    if (!profileSelect) return;
    const profileName = profileSelect.value;

    if (!profileName) {
        alert('Please select a profile to delete.');
        return;
    }

    if (confirm(`Are you sure you want to delete profile "${profileName}"?`)) {
        let ok = false;
        try {
            if (nexusProfilesLoaded) {
                const pid = nexusIdsByName.get(profileName);
                if (typeof pid !== 'number') {
                    alert('Unable to resolve Nexus profile ID. Try refreshing profiles.');
                    return;
                }
                const base = getNexusBase();
                const res = await fetch(`${base}/api/profiles/${pid}`, { method: 'DELETE' });
                ok = res.ok;
            } else {
                const res = await fetch(`/api/profiles/${encodeURIComponent(profileName)}`, { method: 'DELETE' });
                ok = res.ok;
            }
        } catch (e) {
            ok = false;
        }

        if (ok) {
            alert(`Profile "${profileName}" deleted successfully.`);
            // Update local Nexus caches if present
            if (nexusProfilesLoaded) {
                nexusProfilesByName.delete(profileName);
                nexusIdsByName.delete(profileName);
            }
            await loadProfiles();
            const details = document.getElementById('profile-details');
            if (details) details.innerHTML = '';
        } else {
            alert('Error deleting profile.');
        }
    }
}

// --- Main Setup Function ---

// --- RAM Configuration UI Functions ---

function setupRamConfigurationUI() {
    const ramSizeModeRadios = document.querySelectorAll('input[name="ram-size-mode"]');
    const ramTestSizeInput = document.getElementById('ram-test-size');
    const ramTestDurationInput = document.getElementById('ram-test-duration');
    const ramSizeUnit = document.getElementById('ram-size-unit');
    const ramConfigPreview = document.getElementById('ram-config-preview');

    // Add event listeners for mode switching
    ramSizeModeRadios.forEach(radio => {
        radio.addEventListener('change', handleRamSizeModeChange);
    });

    // Add event listeners for real-time validation and preview
    if (ramTestSizeInput) {
        ramTestSizeInput.addEventListener('input', handleRamSizeInputChange);
        ramTestSizeInput.addEventListener('blur', validateRamSizeInput);
    }

    if (ramTestDurationInput) {
        ramTestDurationInput.addEventListener('input', validateRamDurationInput);
        ramTestDurationInput.addEventListener('blur', validateRamDurationInput);
    }

    // Add event listener for test selection changes to show/hide RAM config
    const testCheckboxes = document.getElementById('profile-tests-checkboxes');
    if (testCheckboxes) {
        testCheckboxes.addEventListener('change', updateRamConfigVisibility);
    }
}

function handleRamSizeModeChange() {
    const selectedMode = document.querySelector('input[name="ram-size-mode"]:checked')?.value;
    const ramTestSizeInput = document.getElementById('ram-test-size');
    const ramSizeUnit = document.getElementById('ram-size-unit');

    if (selectedMode === 'percentage') {
        ramTestSizeInput.min = '1';
        ramTestSizeInput.max = '50';
        ramTestSizeInput.value = Math.min(parseInt(ramTestSizeInput.value) || 25, 50);
        ramSizeUnit.textContent = '%';
    } else if (selectedMode === 'absolute') {
        ramTestSizeInput.min = '1';
        ramTestSizeInput.removeAttribute('max');
        ramTestSizeInput.value = ramTestSizeInput.value || '1024';
        ramSizeUnit.textContent = 'MB';
    }

    validateRamSizeInput();
    updateRamConfigPreview();
}

function handleRamSizeInputChange() {
    validateRamSizeInput();
    updateRamConfigPreview();
}

function validateRamSizeInput() {
    const ramTestSizeInput = document.getElementById('ram-test-size');
    const selectedMode = document.querySelector('input[name="ram-size-mode"]:checked')?.value;
    const value = parseInt(ramTestSizeInput.value);

    // Remove existing error classes and messages
    ramTestSizeInput.classList.remove('error');
    const existingError = ramTestSizeInput.parentNode.querySelector('.ram-config-error');
    if (existingError) {
        existingError.remove();
    }

    let isValid = true;
    let errorMessage = '';

    if (isNaN(value) || value <= 0) {
        isValid = false;
        errorMessage = 'Please enter a positive number';
    } else if (selectedMode === 'percentage' && (value < 1 || value > 50)) {
        isValid = false;
        errorMessage = 'Percentage must be between 1% and 50%';
    } else if (selectedMode === 'absolute' && value < 1) {
        isValid = false;
        errorMessage = 'Size must be at least 1 MB';
    }

    if (!isValid) {
        ramTestSizeInput.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'ram-config-error';
        errorDiv.textContent = errorMessage;
        ramTestSizeInput.parentNode.appendChild(errorDiv);
    }

    return isValid;
}

function validateRamDurationInput() {
    const ramTestDurationInput = document.getElementById('ram-test-duration');
    const value = parseInt(ramTestDurationInput.value);

    // Remove existing error classes and messages
    ramTestDurationInput.classList.remove('error');
    const existingError = ramTestDurationInput.parentNode.querySelector('.ram-config-error');
    if (existingError) {
        existingError.remove();
    }

    let isValid = true;
    let errorMessage = '';

    if (isNaN(value) || value < 5 || value > 3600) {
        isValid = false;
        errorMessage = 'Duration must be between 5 and 3600 seconds';
    }

    if (!isValid) {
        ramTestDurationInput.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'ram-config-error';
        errorDiv.textContent = errorMessage;
        ramTestDurationInput.parentNode.appendChild(errorDiv);
    }

    return isValid;
}

async function updateRamConfigPreview() {
    const selectedMode = document.querySelector('input[name="ram-size-mode"]:checked')?.value;
    const ramTestSizeInput = document.getElementById('ram-test-size');
    const ramConfigPreview = document.getElementById('ram-config-preview');
    const value = parseInt(ramTestSizeInput.value);

    if (!ramConfigPreview || isNaN(value)) {
        if (ramConfigPreview) ramConfigPreview.style.display = 'none';
        return;
    }

    if (selectedMode === 'percentage') {
        try {
            // Fetch current system memory info for preview calculation
            const memoryInfo = await fetchData('/api/system/memory_info');
            if (memoryInfo && memoryInfo.available_mb) {
                const calculatedMB = Math.floor(memoryInfo.available_mb * value / 100);
                ramConfigPreview.innerHTML = `
                    <strong>Preview:</strong> ${value}% of ${Math.floor(memoryInfo.available_mb)} MB available = ~${calculatedMB} MB<br>
                    <small>Actual value calculated at test runtime based on available memory</small>
                `;
                ramConfigPreview.style.display = 'block';
            } else {
                ramConfigPreview.innerHTML = `
                    <strong>Preview:</strong> ${value}% of available RAM<br>
                    <small>Actual MB value will be calculated at test runtime</small>
                `;
                ramConfigPreview.style.display = 'block';
            }
        } catch (error) {
            ramConfigPreview.innerHTML = `
                <strong>Preview:</strong> ${value}% of available RAM<br>
                <small>Actual MB value will be calculated at test runtime</small>
            `;
            ramConfigPreview.style.display = 'block';
        }
    } else {
        ramConfigPreview.innerHTML = `<strong>Preview:</strong> Fixed ${value} MB will be used for RAM test`;
        ramConfigPreview.style.display = 'block';
    }
}

function updateRamConfigVisibility() {
    const ramConfigSection = document.getElementById('ram-config-section');
    const testCheckboxes = document.querySelectorAll('input[name="profile-test"]:checked');
    
    if (!ramConfigSection) return;

    // Check if any RAM tests are selected
    const ramTestPaths = [
        'agent.tests.visual_ram_test.run_visual_ram_test',
        'agent.tests.web_visual_ram_test.run_web_visual_ram_test'
    ];

    const hasRamTests = Array.from(testCheckboxes).some(checkbox => 
        ramTestPaths.includes(checkbox.value)
    );

    if (hasRamTests) {
        ramConfigSection.style.display = 'block';
        updateRamConfigPreview();
    } else {
        ramConfigSection.style.display = 'none';
    }
}

function getRamConfigFromForm() {
    const ramConfigSection = document.getElementById('ram-config-section');
    if (!ramConfigSection || ramConfigSection.style.display === 'none') {
        return null;
    }

    const selectedMode = document.querySelector('input[name="ram-size-mode"]:checked')?.value;
    const testSize = parseInt(document.getElementById('ram-test-size').value);
    const duration = parseInt(document.getElementById('ram-test-duration').value);

    if (!selectedMode || isNaN(testSize) || isNaN(duration)) {
        return null;
    }

    return {
        test_size_mode: selectedMode,
        test_size_value: testSize,
        duration_seconds: duration
    };
}

function setRamConfigInForm(ramConfig) {
    if (!ramConfig) {
        // Set defaults
        document.querySelector('input[name="ram-size-mode"][value="percentage"]').checked = true;
        document.getElementById('ram-test-size').value = '25';
        document.getElementById('ram-test-duration').value = '30';
    } else {
        const modeRadio = document.querySelector(`input[name="ram-size-mode"][value="${ramConfig.test_size_mode}"]`);
        if (modeRadio) modeRadio.checked = true;
        
        document.getElementById('ram-test-size').value = ramConfig.test_size_value || 25;
        document.getElementById('ram-test-duration').value = ramConfig.duration_seconds || 30;
    }

    handleRamSizeModeChange();
    updateRamConfigVisibility();
}

// --- CPU Configuration UI Functions ---

function setupCpuTestConfiguration() {
    const cpuTestDurationInput = document.getElementById('cpu-test-duration');
    const cpuLoadIntensitySlider = document.getElementById('cpu-load-intensity');
    const cpuIntensityValue = document.getElementById('cpu-intensity-value');

    // Add event listeners for CPU duration input
    if (cpuTestDurationInput) {
        cpuTestDurationInput.addEventListener('input', validateCpuDuration);
        cpuTestDurationInput.addEventListener('blur', validateCpuDuration);
        cpuTestDurationInput.addEventListener('input', updateCpuConfigPreview);
    }

    // Add event listener for CPU intensity slider
    if (cpuLoadIntensitySlider && cpuIntensityValue) {
        cpuLoadIntensitySlider.addEventListener('input', function() {
            cpuIntensityValue.textContent = this.value + '%';
            updateCpuConfigPreview();
        });
    }

    // Add event listener for test selection changes to show/hide CPU config
    const testCheckboxes = document.getElementById('profile-tests-checkboxes');
    if (testCheckboxes) {
        testCheckboxes.addEventListener('change', updateCpuConfigVisibility);
    }
}

function validateCpuDuration() {
    const cpuTestDurationInput = document.getElementById('cpu-test-duration');
    const value = parseInt(cpuTestDurationInput.value);

    // Remove existing error classes and messages
    cpuTestDurationInput.classList.remove('error');
    const existingError = cpuTestDurationInput.parentNode.querySelector('.cpu-config-error');
    if (existingError) {
        existingError.remove();
    }

    let isValid = true;
    let errorMessage = '';

    if (isNaN(value) || value < 5 || value > 300) {
        isValid = false;
        errorMessage = 'Duration must be between 5 and 300 seconds';
    }

    if (!isValid) {
        cpuTestDurationInput.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'cpu-config-error';
        errorDiv.textContent = errorMessage;
        cpuTestDurationInput.parentNode.appendChild(errorDiv);
    }

    return isValid;
}

function validateCpuIntensity() {
    const cpuLoadIntensitySlider = document.getElementById('cpu-load-intensity');
    const value = parseInt(cpuLoadIntensitySlider.value);

    // Remove existing error classes and messages
    cpuLoadIntensitySlider.classList.remove('error');
    const existingError = cpuLoadIntensitySlider.parentNode.querySelector('.cpu-config-error');
    if (existingError) {
        existingError.remove();
    }

    let isValid = true;
    let errorMessage = '';

    if (isNaN(value) || value < 50 || value > 100) {
        isValid = false;
        errorMessage = 'CPU intensity must be between 50% and 100%';
    }

    if (!isValid) {
        cpuLoadIntensitySlider.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'cpu-config-error';
        errorDiv.textContent = errorMessage;
        cpuLoadIntensitySlider.parentNode.appendChild(errorDiv);
    }

    return isValid;
}

function validateCpuTestConfig() {
    const cpuConfigSection = document.getElementById('cpu-config-section');
    if (!cpuConfigSection || cpuConfigSection.style.display === 'none') {
        return true; // No validation needed if section is hidden
    }

    const durationValid = validateCpuDuration();
    const intensityValid = validateCpuIntensity();

    return durationValid && intensityValid;
}

function updateCpuConfigPreview() {
    const cpuConfigPreview = document.getElementById('cpu-config-preview');
    const duration = document.getElementById('cpu-test-duration').value;
    const intensity = document.getElementById('cpu-load-intensity').value;

    if (!cpuConfigPreview) return;

    const durationValue = parseInt(duration);
    const intensityValue = parseInt(intensity);

    if (isNaN(durationValue) || isNaN(intensityValue)) {
        cpuConfigPreview.style.display = 'none';
        return;
    }

    cpuConfigPreview.innerHTML = `
        <div class="config-preview">
            <strong>Configuration Preview:</strong><br>
            Duration: ${duration} seconds<br>
            CPU Load: ${intensity}%<br>
            <em>Estimated test time: ~${duration}s</em>
        </div>
    `;
    cpuConfigPreview.style.display = 'block';
}

function updateCpuConfigVisibility() {
    const cpuConfigSection = document.getElementById('cpu-config-section');
    const testCheckboxes = document.querySelectorAll('input[name="profile-test"]:checked');
    
    if (!cpuConfigSection) return;

    // Check if any CPU tests are selected
    const cpuTestPaths = [
        'agent.tests.visual_cpu_test.visual_cpu_test'
    ];

    const hasCpuTests = Array.from(testCheckboxes).some(checkbox => 
        cpuTestPaths.includes(checkbox.value)
    );

    if (hasCpuTests) {
        cpuConfigSection.style.display = 'block';
        updateCpuConfigPreview();
    } else {
        cpuConfigSection.style.display = 'none';
    }
}

function getCpuConfigFromForm() {
    const cpuConfigSection = document.getElementById('cpu-config-section');
    if (!cpuConfigSection || cpuConfigSection.style.display === 'none') {
        return null;
    }

    const duration = parseInt(document.getElementById('cpu-test-duration').value);
    const intensity = parseInt(document.getElementById('cpu-load-intensity').value);

    if (isNaN(duration) || isNaN(intensity)) {
        return null;
    }

    return {
        duration_seconds: duration,
        cpu_load_intensity: intensity
    };
}

function setCpuConfigInForm(cpuConfig) {
    if (!cpuConfig) {
        // Set defaults
        document.getElementById('cpu-test-duration').value = '20';
        document.getElementById('cpu-load-intensity').value = '100';
        document.getElementById('cpu-intensity-value').textContent = '100%';
    } else {
        document.getElementById('cpu-test-duration').value = cpuConfig.duration_seconds || 20;
        document.getElementById('cpu-load-intensity').value = cpuConfig.cpu_load_intensity || 100;
        document.getElementById('cpu-intensity-value').textContent = (cpuConfig.cpu_load_intensity || 100) + '%';
    }

    updateCpuConfigVisibility();
    updateCpuConfigPreview();
}

export function setupProfileManagement(checkRunTestButtonState) {
    // Initial load (works with or without the old Profile Management section present)
    loadProfiles();
    setupProfileModal();

    // Button listeners (old Profile Management section controls may not exist anymore)
    const loadProfileDetailsBtn = document.getElementById('load-profile-details-btn');
    if (loadProfileDetailsBtn) loadProfileDetailsBtn.addEventListener('click', displaySelectedProfileDetails);

    const createProfileBtn = document.getElementById('create-profile-btn');
    if (createProfileBtn) createProfileBtn.addEventListener('click', openProfileModalForCreate);

    const editProfileBtn = document.getElementById('edit-profile-btn');
    if (editProfileBtn) editProfileBtn.addEventListener('click', openProfileModalForEdit);

    const deleteProfileBtn = document.getElementById('delete-profile-btn');
    if (deleteProfileBtn) deleteProfileBtn.addEventListener('click', deleteSelectedProfile);

    // Select dropdown listeners
    const profileSelect = document.getElementById('profile-select'); // Removed section may make this null
    const profileSelectQuick = document.getElementById('profile-select-quick');

    if (profileSelect) {
        profileSelect.addEventListener('change', () => {
            const selectedProfileName = profileSelect.value;
            const isProfileSelected = !!selectedProfileName;
            if (editProfileBtn) editProfileBtn.disabled = !isProfileSelected;
            if (deleteProfileBtn) deleteProfileBtn.disabled = !isProfileSelected;
            const detailsDiv = document.getElementById('profile-details');
            if (!isProfileSelected && detailsDiv) {
                detailsDiv.innerHTML = '';
            }
            if (profileSelectQuick) profileSelectQuick.value = selectedProfileName;
            if (checkRunTestButtonState) checkRunTestButtonState();
        });
    }

    if (profileSelectQuick) {
        profileSelectQuick.addEventListener('change', () => {
            const selectedProfileName = profileSelectQuick.value;
            if (profileSelect) profileSelect.value = selectedProfileName;
            // Profile details preview removed - details are managed in Nexus
            if (checkRunTestButtonState) checkRunTestButtonState();
        });
    }

    // Ensure open function is reachable if other modules need it
    window.openProfileModalForEdit = openProfileModalForEdit;
    window.openProfileModalForCreate = openProfileModalForCreate;

    // Note: load-profile-details-quick and manage-profiles-btn buttons have been removed
    // Profile management is now handled through Nexus, so these buttons are no longer needed
}
