import { fetchData } from './utils.js';

let defaultDeviceConditionsStructure = null;

function fetchDefaultDeviceConditionStructureIfNeeded() {
    if (!defaultDeviceConditionsStructure) {
            // Fallback structure modeled after legacy desktop GUI but using backend keys
            defaultDeviceConditionsStructure = {
                "Physical Condition": {
                    "case_condition": { label: "Case Condition", options: ["Good", "Light Scratches", "Heavy Scratches", "Light Dents", "Heavy Dents", "Cracks"] },
                    "screen_condition": { label: "Screen Condition", options: ["Good", "Light Scratches", "Heavy Scratches", "Cracked", "Pressure Marks", "Faded Areas", "Dead Pixels"] },
                    "keyboard_condition": { label: "Keyboard Condition", options: ["Good", "Light Wear", "Heavy Wear"] },
                    "touchpad_condition": { label: "Touchpad Condition", options: ["Good", "Light Wear", "Heavy Wear"] },
                    "usb_ports": { label: "USB Ports", options: ["Good", "Damaged"] }
                },
                "Missing Items": {
                    "missing_items": { label: "Missing Items", options: ["N/A", "Point Stick", "Footpads", "Keyboard Keys", "Bottom Screws"], multi: true }
                },
                "Grade": {
                    "grade": { label: "Grade", options: ["A", "B", "C", "Damaged"] }
                },
                "Notes": "" // General notes (flat key "notes" will be used)
            };
            // Using local default structure – no network request necessary.
    }
    return defaultDeviceConditionsStructure;
}

function buildDeviceConditionForm(conditions, structure) {
    const formArea = document.getElementById('dc-form-area');
    formArea.innerHTML = ''; // Clear previous form

    for (const category in structure) {
        const categoryDiv = document.createElement('div');
        categoryDiv.classList.add('dc-category');
        categoryDiv.setAttribute('data-category', category);

        const categoryTitle = document.createElement('h4');
        categoryTitle.textContent = category;
        categoryDiv.appendChild(categoryTitle);

        if (category === "Notes") { // Special handling for Notes string
            const notesTextarea = document.createElement('textarea');
            notesTextarea.id = `dc-input-Notes`;
            notesTextarea.name = `Notes`;
            notesTextarea.value = conditions[category] || structure[category] || "";
            notesTextarea.rows = 3;
            notesTextarea.style.width = "90%";

            const label = document.createElement('label');
            label.htmlFor = notesTextarea.id;
            label.textContent = "Notes:";
            label.style.display = "block";

            categoryDiv.appendChild(label);
            categoryDiv.appendChild(notesTextarea);
        } else {
            for (const item in structure[category]) {
                const itemDiv = document.createElement('div');
                itemDiv.classList.add('dc-item');

                const config = structure[category][item];

                const label = document.createElement('label');
                label.htmlFor = `dc-input-${category}-${item}`;
                const displayLabel = (typeof config === 'object' && config.label) ? config.label : item.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
                label.textContent = `${displayLabel}:`;
                let input;

                // Determine input type based on structure config
                if (Array.isArray(config)) {
                    // Simple dropdown select
                    input = document.createElement('select');
                    config.forEach(opt => {
                        const optionEl = document.createElement('option');
                        optionEl.value = opt;
                        optionEl.textContent = opt;
                        input.appendChild(optionEl);
                    });
                    const currentVal = (conditions[item] !== undefined ? conditions[item] : ((conditions[category] && conditions[category][item]) || config[0]));
                    input.value = currentVal;
                } else if (typeof config === 'object' && config.options) {
                    // Dropdown or multi-select depending on "multi" flag
                    input = document.createElement('select');
                    if (config.multi) {
                        input.multiple = true;
                        input.size = Math.min(config.options.length, 6);
                    }
                    config.options.forEach(opt => {
                        const optionEl = document.createElement('option');
                        optionEl.value = opt;
                        optionEl.textContent = opt;
                        input.appendChild(optionEl);
                    });
                    const currentValRaw = (conditions[item] !== undefined ? conditions[item] : ((conditions[category] && conditions[category][item]) || ""));
                    const currentVals = currentValRaw.split(',').map(s => s.trim());
                    [...input.options].forEach(o => {
                        if (currentVals.includes(o.value)) {
                            o.selected = true;
                        }
                    });
                } else {
                    // Fallback to text input
                    input = document.createElement('input');
                    input.type = 'text';
                    input.value = (conditions[category] && conditions[category][item] !== undefined) ? conditions[category][item] : (config || "");
                }

                input.id = `dc-input-${category}-${item}`;
                input.name = `${category}-${item}`;

                itemDiv.appendChild(label);
                itemDiv.appendChild(input);
                categoryDiv.appendChild(itemDiv);
            }
        }
        formArea.appendChild(categoryDiv);
    }
    document.getElementById('save-dc-btn').style.display = 'inline-block'; // Show save button
}

async function loadDeviceConditions() {
    const assetNumInput = document.getElementById('dc-asset-number');
    const formArea = document.getElementById('dc-form-area');
    const dcStatus = document.getElementById('dc-status');
    const saveBtn = document.getElementById('save-dc-btn');

    if (!assetNumInput || !formArea || !dcStatus || !saveBtn) return;
    const assetNum = assetNumInput.value.trim();

    if (!assetNum) {
        alert("Please enter an Asset Number to load conditions.");
        formArea.innerHTML = '<p>Load or enter an asset number to manage device conditions.</p>';
        saveBtn.style.display = 'none';
        dcStatus.textContent = '';
        return;
    }

    formArea.innerHTML = `<p>Loading conditions for asset: ${assetNum}...</p>`;
    dcStatus.textContent = '';
    saveBtn.style.display = 'none';

    let conditions = await fetchData(`/api/device_conditions/${assetNum}`);
    const structure = fetchDefaultDeviceConditionStructureIfNeeded();

    if (!structure) {
        formArea.innerHTML = '<p style="color:red;">Error: Could not load device condition structure. Cannot build form.</p>';
        return;
    }

    if (conditions && conditions.default_structure_if_needed) {
        console.log("No specific conditions found, using default structure for new entry.");
        conditions = {}; // Start with empty conditions, form will use defaults from structure
    } else if (conditions === null || (conditions && conditions.error && !conditions.default_structure_if_needed)) {
        formArea.innerHTML = `<p style="color:red;">Error loading conditions for ${assetNum}. Check console.</p>`;
        console.log("Error loading conditions, but attempting to show a blank form with default structure.");
        conditions = {}; // Treat as new/empty
    }

    if (conditions === null || conditions.error) {
        conditions = {};
    }

    buildDeviceConditionForm(conditions, structure);
}

async function saveDeviceConditions() {
    const dcStatus = document.getElementById('dc-status');
    const assetNumber = document.getElementById('asset-number').value;

    if (!assetNumber) {
        alert("Asset Number is missing. Cannot save conditions.");
        return;
    }

    const structure = fetchDefaultDeviceConditionStructureIfNeeded();
    if (!structure) {
        if (dcStatus) {
            dcStatus.textContent = 'Error: Device condition structure not available. Cannot save.';
            dcStatus.className = 'status-message error';
        }
        return;
    }

    const conditionsToSave = {};
    for (const category in structure) {
        if (category === "Notes") {
            const notesTextarea = document.getElementById(`dc-input-Notes`);
            conditionsToSave["notes"] = notesTextarea ? notesTextarea.value : "";
            continue;
        }
        for (const itemKey in structure[category]) {
            const inputElement = document.getElementById(`dc-input-${category}-${itemKey}`);
            let valueToSave;
            if (inputElement) {
                if (inputElement.tagName === 'SELECT') {
                    if (inputElement.multiple) {
                        const selected = Array.from(inputElement.selectedOptions).map(o => o.value);
                        valueToSave = selected.join(',');
                    } else {
                        valueToSave = inputElement.value;
                    }
                } else {
                    valueToSave = inputElement.value;
                }
            } else {
                console.warn(`Input element for ${category}-${itemKey} not found! Using default.`);
                const cfg = structure[category][itemKey];
                if (Array.isArray(cfg)) {
                    valueToSave = cfg[0];
                } else if (typeof cfg === 'object' && cfg.options) {
                    valueToSave = cfg.options[0];
                } else {
                    valueToSave = "";
                }
            }
            conditionsToSave[itemKey] = valueToSave;
        }
    }

    if (dcStatus) {
        dcStatus.textContent = 'Saving...';
        dcStatus.className = 'status-message info';
    }

    const response = await fetch(`/api/device_conditions/${assetNumber}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(conditionsToSave)
    });

    const result = await response.json();
    if (dcStatus) {
        if (response.ok) {
            dcStatus.textContent = result.message || 'Conditions saved successfully!';
            dcStatus.className = 'status-message success';


        } else {
            dcStatus.textContent = `Error: ${result.error || 'Failed to save conditions.'}`;
            dcStatus.className = 'status-message error';
        }
    }
}

// Obsolete modal-related code removed to simplify inline device condition UI.


// Provide no-op stubs in case legacy markup still references these
window.closeDeviceConditionsModal = () => {};
window.openDeviceConditionsModal = () => {};

export async function setupDeviceConditionsModule() {
    // Build the form immediately on load using default structure (and empty current conditions)
    const structure = fetchDefaultDeviceConditionStructureIfNeeded();
    if (structure) {
        buildDeviceConditionForm({}, structure);
    }

    // Attach save handler
    const saveDcBtn = document.getElementById('save-dc-btn');
    if (saveDcBtn) {
        saveDcBtn.addEventListener('click', saveDeviceConditions);
    }
}
