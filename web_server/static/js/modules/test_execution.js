// test_execution.js
import { launchVisualTest, runVisualTestsFromProfile } from './visual_tests.js';
// This module handles running test profiles, polling for logs, and managing the UI state during tests.

let logPollInterval = null; // Legacy fallback polling (not used with SSE)
let logEventSource = null;   // Active EventSource instance
let lastLogIndex = -1;       // Used only for fallback polling
let currentPollingAssetNumber = null;

function stopLogStreaming() {
    if (logEventSource) {
        logEventSource.close();
        logEventSource = null;
        console.log("Closed log EventSource stream.");
    }
    if (logPollInterval) {
        clearInterval(logPollInterval);
        logPollInterval = null;
        console.log("Stopped legacy log polling.");
    }
}

// Legacy polling function - no longer used since we're using Enhanced Test Monitor
// Kept for reference but functionality moved to Enhanced Test Monitor
async function pollLogs() {
    // This function is deprecated - Enhanced Test Monitor handles all logging now
    console.log('pollLogs called but deprecated - Enhanced Test Monitor handles logging');
    return;
}

export function checkRunTestButtonState() {
    const runTestsBtn = document.getElementById('run-tests-btn');
    const runTestsBtnQuick = document.getElementById('run-tests-btn-quick');

    const currentOperatorId = document.getElementById('operator-id').value;
    const currentAssetNumber = document.getElementById('asset-number').value;
    const currentProfileName = document.getElementById('profile-select-quick')?.value ||
                              document.getElementById('profile-select')?.value;

    const shouldDisable = (logEventSource || logPollInterval) || !currentOperatorId || !currentAssetNumber || !currentProfileName;

    if (runTestsBtn) runTestsBtn.disabled = shouldDisable;
    if (runTestsBtnQuick) runTestsBtnQuick.disabled = shouldDisable;
}

async function runTests() {
    console.log('=== runTests() called ===');

    const currentOperatorId = document.getElementById('operator-id').value;
    currentPollingAssetNumber = document.getElementById('asset-number').value;
    const currentProfileName = document.getElementById('profile-select-quick')?.value ||
                              document.getElementById('profile-select')?.value;

    // Check license status before proceeding
    try {
        const { checkLicenseForOperations } = await import('./license_status.js');
        const licenseAllowed = await checkLicenseForOperations();

        if (!licenseAllowed) {
            alert('License Required\n\nNo valid licenses available for test operations.\n\nTo purchase licenses:\n1. Visit the Nexus website\n2. Log in to your account\n3. Purchase credits or activate a server license\n4. Configure your API key in Crucible');
            return;
        }
    } catch (error) {
        console.warn('License check failed, proceeding anyway:', error);
    }
    
    // Trigger enhanced test monitor if available
    if (window.enhancedTestMonitor) {
        const event = new CustomEvent('testExecutionStarted', {
            detail: {
                assetNumber: currentPollingAssetNumber,
                operatorId: currentOperatorId,
                profileName: currentProfileName
            }
        });
        document.dispatchEvent(event);
        console.log('Enhanced Test Monitor triggered');
    }

    console.log(`runTests: operator=${currentOperatorId}, asset=${currentPollingAssetNumber}, profile=${currentProfileName}`);

    if (!currentOperatorId || !currentPollingAssetNumber || !currentProfileName) {
        alert('Please ensure Operator ID, Asset Number are filled and a Profile is selected.');
        currentPollingAssetNumber = null;
        return;
    }

    // Enhanced Test Monitor will handle all UI updates and logging
    checkRunTestButtonState();
    stopLogStreaming();
    lastLogIndex = -1;

    const includeVisual = true; // Always include visual tests

    const payload = {
        asset_number: currentPollingAssetNumber,
        operator_id: currentOperatorId,
        profile_name: currentProfileName,
        include_visual: includeVisual
    };

    try {
        const response = await fetch('/api/run_tests', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        const result = await response.json();

        if (response.ok) {
            console.log(`Test execution request successful: ${result.message}`);

            // Enhanced Test Monitor will handle all log streaming and real-time updates
            // No need for manual EventSource or polling here
            // After orchestrator started, optionally launch visual tests selected in profile
            if (includeVisual) {
                console.log('=== Calling runVisualTestsFromProfile ===');
                runVisualTestsFromProfile(currentProfileName, currentPollingAssetNumber);
            }
        } else {
            console.error(`Error starting tests: ${result.error} ${result.details || ''}`);

            // Handle license-specific errors
            if (response.status === 402) {
                // License required error
                const licenseMessage = result.details || 'No valid licenses available for operations.';
                const guidance = 'To purchase licenses:\n1. Visit the Nexus website\n2. Log in to your account\n3. Purchase credits or activate a server license\n4. Configure your API key in Crucible';
                alert(`License Required\n\n${licenseMessage}\n\n${guidance}`);
            } else if (response.status === 503 && result.error === 'License Validation Error') {
                // License validation error
                alert(`License Validation Error\n\n${result.details}\n\nPlease check your Nexus server connection.`);
            } else {
                // Other errors
                alert(`Error starting tests: ${result.error}\n${result.details || ''}`);
            }

            currentPollingAssetNumber = null;
            checkRunTestButtonState();
        }
    } catch (error) {
        console.error('Failed to run tests:', error);
        currentPollingAssetNumber = null;
        checkRunTestButtonState();
    }
}

// clearTestLog function removed - Enhanced Test Monitor handles all clearing

function updateAssetStatus() {
    const statusElement = document.getElementById('asset-status');
    const statusText = document.getElementById('asset-status-text');
    const operatorId = document.getElementById('operator-id').value;
    const assetNumber = document.getElementById('asset-number').value;

    if (statusElement && statusText) {
        if (operatorId && assetNumber) {
            statusElement.className = 'asset-status ready';
            statusText.textContent = `Ready: ${operatorId} / ${assetNumber}`;
        } else {
            statusElement.className = 'asset-status incomplete';
            statusText.textContent = 'Enter asset information to begin';
        }
    }
}

function handleAssetInfoChange() {
    updateAssetStatus();
    checkRunTestButtonState();
}

function handleEnterKey(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        const operatorId = document.getElementById('operator-id').value;
        const assetNumber = document.getElementById('asset-number').value;

        if (event.target.id === 'operator-id' && operatorId) {
            document.getElementById('asset-number').focus();
        } else if (event.target.id === 'asset-number' && assetNumber && operatorId) {
            const profileSelect = document.getElementById('profile-select-quick');
            if (profileSelect) profileSelect.focus();
        }
    }
}

export function setupTestExecution() {
    const operatorIdInput = document.getElementById('operator-id');
    const assetNumberInput = document.getElementById('asset-number');
    const runTestsBtn = document.getElementById('run-tests-btn');
    const runTestsBtnQuick = document.getElementById('run-tests-btn-quick');
    // clearLogBtn removed - Enhanced Test Monitor handles clearing

    if (operatorIdInput) {
        operatorIdInput.addEventListener('input', handleAssetInfoChange);
        operatorIdInput.addEventListener('keypress', handleEnterKey);
    }
    if (assetNumberInput) {
        assetNumberInput.addEventListener('input', handleAssetInfoChange);
        assetNumberInput.addEventListener('keypress', handleEnterKey);
    }

    if (runTestsBtn) runTestsBtn.addEventListener('click', runTests);
    if (runTestsBtnQuick) runTestsBtnQuick.addEventListener('click', runTests);

    document.getElementById('profile-select-quick')?.addEventListener('change', checkRunTestButtonState);
    document.getElementById('profile-select')?.addEventListener('change', checkRunTestButtonState);

    updateAssetStatus();
    checkRunTestButtonState();
}
