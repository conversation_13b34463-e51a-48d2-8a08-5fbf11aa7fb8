.has-mountpoints {
    background-color: #44222233;
}
/* RAM Test Configuration Styles */
.ram-test-config {
    margin-top: 1rem;
}

.ram-config-section {
    border: 1px solid #444;
    border-radius: 4px;
    padding: 1rem;
    margin-top: 0.5rem;
    background-color: rgba(255, 255, 255, 0.02);
}

.size-mode-selection {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.radio-label input[type="radio"] {
    margin: 0;
}

.ram-size-input, .ram-duration-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.ram-size-input label, .ram-duration-input label {
    min-width: 80px;
    font-size: 0.9rem;
}

.ram-size-input input, .ram-duration-input input {
    width: 80px;
    padding: 0.25rem;
    border: 1px solid #555;
    border-radius: 3px;
    background-color: #2a2a2a;
    color: #fff;
}

#ram-size-unit {
    font-weight: bold;
    color: #4CAF50;
}

.ram-config-preview {
    margin: 0.75rem 0;
    padding: 0.5rem;
    background-color: rgba(76, 175, 80, 0.1);
    border-left: 3px solid #4CAF50;
    border-radius: 3px;
    font-size: 0.85rem;
    display: none;
}

.ram-config-help {
    margin-top: 0.75rem;
    padding: 0.5rem;
    background-color: rgba(255, 193, 7, 0.1);
    border-left: 3px solid #FFC107;
    border-radius: 3px;
}

.ram-config-help small {
    font-size: 0.8rem;
    line-height: 1.4;
    color: #ccc;
}

/* Error states for RAM configuration */
.ram-size-input input.error, .ram-duration-input input.error {
    border-color: #f44336;
    background-color: rgba(244, 67, 54, 0.1);
}

.ram-config-error {
    color: #f44336;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

/* License Status Styles */
.license-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid #444;
    font-size: 0.9rem;
}

.license-label {
    font-weight: bold;
    color: #ccc;
}

.license-valid {
    color: #4caf50;
    font-weight: bold;
}

.license-invalid {
    color: #f44336;
    font-weight: bold;
}

.license-unknown {
    color: #ff9800;
    font-weight: bold;
}

.license-expired {
    color: #ff9800;
    font-weight: bold;
}

.btn-icon {
    background: none;
    border: 1px solid #555;
    color: #ccc;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: #777;
}

.btn-icon:active {
    background-color: rgba(255, 255, 255, 0.2);
}

.btn-icon:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Header layout alignment */
.asset-header {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    justify-content: space-between;
    min-height: 48px; /* Ensure consistent height */
}

.asset-header > * {
    display: flex;
    align-items: center;
}

.asset-header .license-status {
    margin-left: auto;
}

/* Inline Test Controls */
.test-controls-inline {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    height: 100%; /* Match parent height */
}

.test-controls-inline label {
    font-weight: bold;
    color: #ccc;
    white-space: nowrap;
    font-size: 0.9rem;
    line-height: 1;
}

.test-controls-inline .profile-select {
    min-width: 180px;
    padding: 0.5rem 0.75rem;
    border: 1px solid #555;
    border-radius: 4px;
    background-color: #2a2a2a;
    color: #fff;
    font-size: 0.9rem;
    height: 36px; /* Match button height */
    box-sizing: border-box;
}

.test-controls-inline .profile-select:focus {
    outline: none;
    border-color: #7c94f7;
    box-shadow: 0 0 0 2px rgba(124, 148, 247, 0.25);
}

.test-controls-inline .btn-primary {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    white-space: nowrap;
    height: 36px; /* Match select height */
    box-sizing: border-box;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Ensure asset status and license status have consistent alignment */
.asset-status,
.license-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    height: 36px; /* Match other elements */
}

/* Responsive adjustments for inline test controls */
@media (max-width: 768px) {
    .asset-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
        min-height: auto;
    }

    .test-controls-inline {
        justify-content: center;
        flex-wrap: wrap;
    }

    .test-controls-inline .profile-select {
        min-width: 150px;
        flex: 1;
    }
}