"""
License validation module for Crucible.

This module handles checking license availability before running operations,
and provides appropriate user feedback when licenses are not available.
"""
from __future__ import annotations
import os
import logging
from typing import Dict, Any, Optional
from agent.core import nexus_credits

logger = logging.getLogger(__name__)


class LicenseValidationResult:
    """Result of license validation check."""
    
    def __init__(self, available: bool, message: str, details: Optional[Dict[str, Any]] = None):
        self.available = available
        self.message = message
        self.details = details or {}
    
    def __bool__(self) -> bool:
        return self.available


def validate_license_for_operations() -> LicenseValidationResult:
    """
    Validate that licenses are available for running operations.
    
    Returns:
        LicenseValidationResult indicating whether operations can proceed
    """
    # Check for development bypass
    dev_bypass = os.getenv("NEXUS_DEV_BYPASS_CREDITS", "").lower() == "true"
    if dev_bypass:
        logger.warning("DEV MODE: Bypassing license validation")
        return LicenseValidationResult(
            available=True,
            message="Development mode - license validation bypassed",
            details={"dev_bypass": True}
        )
    
    # Check license availability
    try:
        license_status = nexus_credits.check_license_availability()
        
        if license_status.get("error"):
            error_msg = license_status["error"]
            logger.error(f"License validation failed: {error_msg}")
            
            # Provide user-friendly error messages
            if "Network error" in error_msg or "API error" in error_msg:
                message = (
                    "Unable to connect to Nexus server to validate license. "
                    "Please check your network connection and Nexus server configuration."
                )
            elif "Invalid API key" in error_msg:
                message = (
                    "Invalid API key. Please check your Nexus API key configuration."
                )
            elif "No API configuration" in error_msg:
                message = (
                    "Nexus API not configured. Please configure your Nexus server connection "
                    "or API key to validate licenses."
                )
            else:
                message = f"License validation error: {error_msg}"
            
            return LicenseValidationResult(
                available=False,
                message=message,
                details=license_status
            )
        
        if not license_status.get("available", False):
            balance = license_status.get("balance", 0)
            license_summary = license_status.get("license_summary", {})
            has_server_license = license_summary.get("hasServerLicense", False)
            maintenance_active = license_summary.get("maintenanceActive", False)
            
            if balance <= 0 and not (has_server_license and maintenance_active):
                if has_server_license and not maintenance_active:
                    message = (
                        "Your server license maintenance has expired. "
                        "Please renew your maintenance or purchase credits to continue operations."
                    )
                elif balance <= 0:
                    message = (
                        "No credits available and no valid server license found. "
                        "Please purchase credits or activate a server license to continue operations."
                    )
                else:
                    message = "No valid licenses available for operations."
                
                return LicenseValidationResult(
                    available=False,
                    message=message,
                    details=license_status
                )
        
        # License validation successful
        balance = license_status.get("balance", 0)
        license_summary = license_status.get("license_summary", {})
        has_server_license = license_summary.get("hasServerLicense", False)
        maintenance_active = license_summary.get("maintenanceActive", False)
        
        if has_server_license and maintenance_active:
            message = f"Server license active. Credits available: {balance}"
        else:
            message = f"Credits available: {balance}"
        
        return LicenseValidationResult(
            available=True,
            message=message,
            details=license_status
        )
        
    except Exception as e:
        logger.error(f"Unexpected error during license validation: {e}")
        return LicenseValidationResult(
            available=False,
            message=f"License validation failed due to unexpected error: {e}",
            details={"exception": str(e)}
        )


def get_license_status_message(validation_result: LicenseValidationResult) -> str:
    """
    Get a formatted status message for display in the UI.
    
    Args:
        validation_result: Result from validate_license_for_operations()
        
    Returns:
        Formatted message string for UI display
    """
    if validation_result.available:
        return f"✓ Licensed: {validation_result.message}"
    else:
        return f"⚠ License Issue: {validation_result.message}"


def should_block_operations(validation_result: LicenseValidationResult) -> bool:
    """
    Determine if operations should be blocked based on license validation.
    
    Args:
        validation_result: Result from validate_license_for_operations()
        
    Returns:
        True if operations should be blocked, False otherwise
    """
    # Always allow operations in dev bypass mode
    if validation_result.details.get("dev_bypass", False):
        return False
    
    # Block operations if license is not available
    return not validation_result.available


def get_license_purchase_guidance() -> str:
    """
    Get guidance message for purchasing licenses.
    
    Returns:
        Message with instructions for purchasing licenses
    """
    return (
        "To purchase licenses:\n"
        "1. Visit the Nexus website\n"
        "2. Log in to your account\n"
        "3. Purchase credits or activate a server license\n"
        "4. Configure your API key in Crucible\n\n"
        "For server licenses, ensure maintenance is active."
    )
